import json
import os
import time
import pandas as pd

from consumers.service.image_generation import library_map
from integrations.constant import ImageURLStatus, ImageURLUpdateSource
from utils.helpers import upload_to_gcs, setup_logger
from utils.redis_utils import GenAIRedis
from utils.third_party_usage_service import ThirdPartyUsageTracker
from utils.utils import add_message, update_table_by_task_status, fetch_task_output_csv, download_file
from utils.utils import poll_leonardo_image_generation


class ImageSheetGenerator:

    def __init__(
            self, pre_signed_url_task_csv, task_id, job_id, total_episodes,
            episode_number, read_connection, write_connection, story_id
    ):
        """
        Initializes the ImageSheetGenerator instance with the provided parameters.

        Args:
            pre_signed_url_task_csv (str): The pre-signed URL for the task's CSV file.
            task_id (str): The task identifier.
            job_id (str): The job identifier.
            total_episodes (int): The total number of episodes for the task.
            episode_number (int): Current episode number for the task.
            read_connection (ConnectionPool): mysql read replica connection
            write_connection (ConnectionPool): mysql write connection
        """
        self.pre_signed_url_task_csv = pre_signed_url_task_csv
        self.task_id = task_id
        self.story_id = story_id
        self.job_id = job_id
        self.total_episodes = total_episodes
        self.episode_number = episode_number

        self.genai_write_connection_pool = write_connection
        self.genai_read_connection_pool = read_connection
        self.redis_conn = GenAIRedis()

    def update_csv_media_url(self, filepath, new_url):
        """
        Updates the CSV file with a new media URL for the given task and episode.

        Args:
            filepath (str): The path to the CSV file to be updated.
            new_url (str): The new media URL to be added to the CSV.
        """
        with open(filepath, 'r', encoding='utf-8') as file:
            rows = [row.split(',') for row in file.readlines()]

        for row in rows:
            if row[2] == str(self.episode_number):
                row[5] = new_url

        with open(filepath, 'w', encoding='utf-8') as file:
            file.writelines(','.join(row) for row in rows)

    def generate_missing_images(self, data):
        self.requeue_for_image_gen(data)
        all_generated, data = self.wait_for_image_generation()
        if not all_generated:
            self.poll_missing_images(data)

    def poll_missing_images(self, data):
        batch_updates = {}
        pending_generation_ids = [row[2] for row in data if row[1] != ImageURLStatus.GENERATED.value]
        add_message(f"Polling for {pending_generation_ids} task_id: {self.task_id}; job_id: {self.job_id}")
        max_iterations = 3
        sleep_time = 10
        for _ in range(max_iterations):
            if not pending_generation_ids:
                break

            for gen_id in pending_generation_ids[:]:
                if gen_id:
                    response, image_result = poll_leonardo_image_generation(
                        generation_id=gen_id, initial_sleep=0, poll_interval=0, max_duration=1
                    )
                    if image_result:
                        batch_updates[gen_id] = image_result
                        pending_generation_ids.remove(gen_id)

            time.sleep(sleep_time)
        if batch_updates:
            self.populate_polled_images(batch_updates)
        if pending_generation_ids:
            add_message(
                f"Timeout reached for images: {pending_generation_ids}, task_id: {self.task_id}; job_id: {self.job_id}")
        else:
            add_message(f"All images generated successfully, task_id: {self.task_id}; job_id: {self.job_id}")

    def requeue_for_image_gen(self, data):
        missing_image_generation_seq = [row[3] for row in data if not row[2]]
        if missing_image_generation_seq:
            add_message(f"Re-queuing for image generation - {missing_image_generation_seq}")
            rhash_key = f'{self.task_id}_{self.story_id}_hset'
            for sequence_number in missing_image_generation_seq:
                thread_key = f"{self.task_id}_{self.story_id}_{sequence_number}_image_gen"
                cached_image_gen_payload = self.redis_conn.get_field_value_from_map(rhash_key, thread_key)
                if cached_image_gen_payload:
                    msg = json.loads(cached_image_gen_payload)
                    thirdPartyUsageTracker = ThirdPartyUsageTracker()
                    thirdPartyUsageTracker.initialize(msg["user_id"], "episodes",
                                                      msg["job_id"],
                                                      language=msg["image_generation_meta"]["language"],
                                                      team_id=msg["team_id"])
                    kwargs = {"mysql_write": self.genai_write_connection_pool}
                    image_api_obj = library_map[msg["image_generation_meta"]["library"]](
                        log=setup_logger("image_generation_log.log", "image_generation_consumer"),
                        message=msg,
                        meta={},
                        **kwargs
                    )
                    msg['retry_count'] = 1
                    generation_id, generation_response = image_api_obj.send_image_request(msg)
                    image_api_obj.update_image_row_in_db(generation_id, generation_response)
            add_message(f"All images re-queued successfully, task_id: {self.task_id}; job_id: {self.job_id}")

    def populate_polled_images(self, batch_updates):
        """
        Populates the database with the URLs of the images that have been generated.

        Args:
            batch_updates (dict): A dictionary containing generation IDs and corresponding image URLs.

        Executes an SQL query to update the image URLs and statuses in the database.
        """
        set_clauses = []
        for generation_id, image_urls in batch_updates.items():
            counter = 0
            gcs_image_urls = []
            for image_url in image_urls:
                filename = f"{generation_id}_{counter}.png"
                try:
                    download_file(image_url[0], filename)
                    gcs_image_url = upload_to_gcs(filename, f"{self.task_id}/{self.job_id}/")
                    if os.path.exists(filename):
                        os.remove(filename)
                except Exception as e:
                    gcs_image_url = image_url[0]
                gcs_image_urls.append(gcs_image_url)
                counter += 1

            json_urls = "JSON_ARRAY(" + ", ".join([f"'{url}'" for url in gcs_image_urls]) + ")"
            set_clause = f"""
                WHEN generation_id = '{generation_id}' THEN {json_urls}
            """
            set_clauses.append(set_clause)
        update_query = f"""
            UPDATE image_urls
            SET image_url = CASE
                {''.join(set_clauses)}
                ELSE image_url END,
                status = CASE
                    {''.join([f" WHEN generation_id = '{gen_id}' THEN '{str(ImageURLStatus.GENERATED.value)}'" for gen_id in batch_updates.keys()])}
                    ELSE status END,
                update_source = '{str(ImageURLUpdateSource.POLLING.value)}'
            WHERE generation_id IN ({', '.join([f"'{gen_id}'" for gen_id in batch_updates.keys()])});
        """
        self.genai_write_connection_pool.update_query(update_query)
        add_message(f"Batch update completed, task_id : {self.task_id}; job_id: {self.job_id}")

    def fetch_image_generation_data(self):
        """
        Fetches image generation data for the specified job ID from the database.

        Returns:
            list: A list of rows from the image_urls_new table, containing image URLs, statuses, generation IDs, and sequence numbers.
        """
        query = f"""
            SELECT image_url, status, generation_id, seq_number
            FROM image_urls 
            WHERE job_id = '{self.job_id}' 
            AND image_type = 'scenes' 
            ORDER BY seq_number
        """
        return self.genai_read_connection_pool.fetch_many(query)

    def wait_for_image_generation(self):
        """
        Waits for all images to be generated by checking the status every 10 seconds for up to 120 seconds.

        Returns:
            tuple: A boolean indicating if all images are generated, and the fetched image generation data.
        """
        data = []
        for _ in range(12):
            data = self.fetch_image_generation_data()
            if all(row[1] == str(ImageURLStatus.GENERATED.value) for row in data):
                return True, data
            # time.sleep(10)

        return False, data

    def update_csv_with_image_urls(self, image_gen_data):
        """
          Updates the CSV file with the generated image URLs.

          Args:
              image_gen_data (list): The image generation data containing URLs for each sequence number.

          Returns:
              str: The pre-signed URL of the updated image sheet CSV.
          """
        file_name = f"{self.job_id}_temp.csv"
        output_file = f"{self.job_id}_data.csv"
        download_file(self.pre_signed_url_task_csv, file_name)
        df = pd.read_csv(file_name)

        if "seq_number" not in df.columns:
            raise ValueError("CSV file must contain a 'seq_number' column.")

        max_urls = max(len(json.loads(row[0])) for row in image_gen_data) if image_gen_data else 0
        for i in range(1, max_urls + 1):
            image_key = f"Image_{i}" if i > 1 else "Image"
            df[image_key] = ""
        image_gen_dict = {row[3]: json.loads(row[0]) for row in image_gen_data}
        updates = []
        for row in df.itertuples(index=False):
            seq_no = row.seq_number
            row_update = {"seq_number": seq_no}

            # Always include the sequence, even if no image was generated
            if seq_no in image_gen_dict:
                # If images exist for this sequence, add them
                for i, url in enumerate(image_gen_dict[seq_no], start=1):
                    image_key = f"Image_{i}" if i > 1 else "Image"
                    row_update[image_key] = url
            else:
                # If no images exist for this sequence, add empty values
                for i in range(1, max_urls + 1):
                    image_key = f"Image_{i}" if i > 1 else "Image"
                    row_update[image_key] = ""

            updates.append(row_update)

        updates_df = pd.DataFrame(updates)

        df = df.drop(columns=["Image"] + [f"Image_{i}" for i in range(2, max_urls + 1)], errors="ignore")
        df = df.merge(updates_df, on="seq_number", how="left")

        df.to_csv(output_file, index=False)
        pre_signed_image_sheet = upload_to_gcs(
            local_file_path=output_file,
            destination_blob_name=f"{self.task_id}/",
        )
        add_message(f"Updated CSV saved to {output_file}")
        for file_to_delete in [output_file, file_name]:
            if os.path.isfile(file_to_delete):
                os.remove(file_to_delete)
        return pre_signed_image_sheet

    def generate_image_sheet(self, is_video_generation_pending=False):
        """
        Generates the image sheet by stitching the images together, updating the CSV, and updating the database.

        Manages the entire process of waiting for image generation, handling missing images, updating the CSV file,
        and finalizing the process by uploading the image sheet and updating the task status in the database.
        """
        add_message(f"Starting image sheet stitching for task_id : {self.task_id}; job_id: {self.job_id}")
        all_generated, data = self.wait_for_image_generation()
        if not all_generated:
            add_message(f"Started polling images for task_id : {self.task_id}; job_id: {self.job_id}")
            self.generate_missing_images(data)

        image_data = self.fetch_image_generation_data()
        pre_signed_image_sheet = self.update_csv_with_image_urls(image_data)
        mode, task_output_csv_file = fetch_task_output_csv(self.task_id)
        self.update_csv_media_url(task_output_csv_file, pre_signed_image_sheet)
        pre_signed_url_task_csv = upload_to_gcs(
            local_file_path=task_output_csv_file,
            destination_blob_name=f"{self.task_id}/",
        )
        midjourney_videos_status = "video-stitching-pending" if is_video_generation_pending else "completed"
        self.genai_write_connection_pool.update_query(
            f"UPDATE midjourney_videos SET status='{midjourney_videos_status}' where job_id='{self.job_id}'"
        )

        self.genai_write_connection_pool.update_query(
                f"UPDATE ai_video_generation_tasks SET video_url='{pre_signed_url_task_csv}' WHERE id='{self.task_id}'"
            )
        update_table_by_task_status(self.task_id, self.total_episodes, is_video_generation_pending)

        add_message(f"Finished image sheet stitching for task_id : {self.task_id}; job_id: {self.job_id}")
        self.redis_conn.remove_key_after_completion(f'{self.task_id}_{self.story_id}_hset')
        return pre_signed_url_task_csv, pre_signed_image_sheet
