# Scene Extraction Pipeline Enhancements

## Overview
Enhanced the scene extraction pipeline to capture background settings and time of day consistently, while making image prompts more objective and suitable for image generation.

## Key Improvements

### 1. New Fields Added

#### scene_extract_1_enhanced.txt (Phase 1)
- **`background_setting`**: Objective description of physical location/environment
  - Examples: "office building lobby", "residential kitchen", "city street at intersection"
  - Focus on tangible elements: buildings, rooms, furniture, objects
  - Avoid subjective descriptions or emotions

- **`time_of_day`**: Standardized time classification
  - Only three values: "Day", "Night", or "Evening"
  - Uses context clues from text, lighting descriptions, narrative cues
  - Maintains consistency with previous events when unclear

#### scene_extract_2_enhanced.txt (Phase 2)
- **`background_setting`**: Passes through exact value from Phase 1
- **`time_of_day`**: Passes through exact value from Phase 1
- Maintains consistency across the pipeline

### 2. Enhanced Visual Descriptions

#### More Objective and Physical
**Before:**
```
"raw_visual_description": "<PERSON> steps in front of the <PERSON><PERSON><PERSON>. The <PERSON><PERSON><PERSON> spins toward him, raising a fist and snarling as he speaks."
```

**After:**
```
"raw_visual_description": "<PERSON> positioned directly in front of <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON>'s body rotating toward <PERSON>, right fist raised to shoulder height, facial muscles tensed with mouth open showing teeth."
```

#### Focus on Image Generation Details
- **Body positions**: "feet planted shoulder-width apart"
- **Spatial relationships**: "positioned directly in front of"
- **Facial expressions**: "facial muscles tensed with mouth open"
- **Directional orientation**: "torso rotating counterclockwise"
- **Physical interactions**: "right shoulder leading the turn"

### 3. Enhanced Visual Logic

#### Action/Mixed Events
- Specific body positions, gestures, spatial relationships
- Physical interactions and movements
- Posture, hand positions, facial muscle tension
- Directional orientation

#### Dialogue Events
- Mouth position, facial expression, body stance
- Gaze direction and physical state while speaking
- Example: "Character standing upright, mouth open, eyes focused forward, slight forward lean of torso"

#### SFX Events
- Visible physical elements and reactions
- Character body language and environmental changes
- Example: "Glass fragments scattered across floor surface, characters with raised shoulders and heads turned away from impact point"

#### Narration Events
- Detailed environmental descriptions
- Physical elements, lighting, spatial arrangement
- Architectural features and object placement

## File Structure

### New Enhanced Files
- `scene_extract_1_enhanced.txt` - Enhanced Phase 1 with new fields
- `scene_extract_2_enhanced.txt` - Enhanced Phase 2 with new fields
- `SCENE_EXTRACTION_ENHANCEMENTS.md` - This documentation

### Updated Existing Files
- `scene_extract_1_improved.txt` - Updated with new fields
- `scene_extract_2_improved.txt` - Updated with new fields

## JSON Schema Changes

### Phase 1 Output Schema
```json
{
  "id": integer,
  "start_time": "HH:MM:SS,ms",
  "end_time": "HH:MM:SS,ms", 
  "event_type": "action|dialogue|mixed|sfx|narration",
  "primary_character": string,
  "original_srt_text": string,
  "background_setting": string,        // NEW
  "time_of_day": "Day|Night|Evening",  // NEW
  "raw_visual_description": string     // ENHANCED
}
```

### Phase 2 Output Schema
```json
{
  "srt_lines": string,
  "text": string,                        // ENHANCED - more objective
  "srt_timestamp": string,
  "characters_present": [string],
  "background_setting": string,        // NEW
  "time_of_day": "Day|Night|Evening"   // NEW
}
```

## Benefits

1. **Consistency**: Background and time of day maintained across pipeline
2. **Objective Descriptions**: More suitable for image generation systems
3. **Physical Detail**: Focus on observable elements for better visual output
4. **Standardization**: Consistent time classification and location descriptions
5. **Image Generation Ready**: Descriptions optimized for AI image generation

## Usage

Use the enhanced versions (`scene_extract_1_enhanced.txt` and `scene_extract_2_enhanced.txt`) for new implementations that require background and time consistency with more objective visual descriptions.

The original improved versions remain available for backward compatibility.
