"""
Standalone Audio to CSV Converter Module
A standalone module for converting audio files to CSV format with subtitles grouped by time frames.

This module uses AssemblyAI for transcription to get word-level timestamps and groups them by time frames.
No dependencies on the existing codebase - completely self-contained.
"""

import os
import csv
import json
import time
import requests
from datetime import datetime, timedelta
from moviepy.editor import AudioFileClip

# Import Google Colab userdata for API key
try:
    from google.colab import userdata
    ASSEMBLYAI_API_KEY = userdata.get('ASSEMBLYAI_API_KEY')
except ImportError:
    # Fallback to environment variable if not in Colab
    ASSEMBLYAI_API_KEY = os.getenv('ASSEMBLYAI_API_KEY')


def ms_to_srt_time(ms):
    """
    Convert milliseconds to SRT time format (HH:MM:SS,mmm)
    
    Args:
        ms (int): Time in milliseconds
        
    Returns:
        str: Formatted timestamp string
    """
    seconds, ms = divmod(ms, 1000)
    minutes, seconds = divmod(seconds, 60)
    hours, minutes = divmod(minutes, 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"


def transcribe_audio_with_words(audio_file, api_key=None, verbose=True):
    """
    Transcribe audio using AssemblyAI and return word-level timestamps.
    
    Args:
        audio_file (str): Path to audio file
        api_key (str): AssemblyAI API key (optional, uses global if not provided)
        verbose (bool): Whether to print progress messages
        
    Returns:
        list: List of word dictionaries with "text", "start", "end" in milliseconds
        
    Raises:
        RuntimeError: If transcription fails
        FileNotFoundError: If audio file doesn't exist
    """
    
    if not os.path.exists(audio_file):
        raise FileNotFoundError(f"Audio file not found: {audio_file}")
    
    # Use global API key if not provided
    if api_key is None:
        api_key = ASSEMBLYAI_API_KEY
    
    if not api_key:
        raise ValueError("AssemblyAI API key not found. Set it in Google Colab secrets or as environment variable.")
    
    base_url = "https://api.assemblyai.com/v2"
    headers = {
        'authorization': api_key,
    }
    
    if verbose:
        print("Uploading audio file...")
    
    # Upload the audio file
    with open(audio_file, "rb") as f:
        response = requests.post(base_url + "/upload", headers=headers, data=f)
    
    if response.status_code != 200:
        raise RuntimeError(f"Upload failed: {response.status_code} - {response.text}")
    
    upload_url = response.json()["upload_url"]
    
    # Start transcription
    data = {
        "audio_url": upload_url,
        "language_code": "en"
    }
    
    if verbose:
        print("Starting transcription...")
    
    url = base_url + "/transcript"
    response = requests.post(url, json=data, headers=headers)
    
    if response.status_code != 200:
        raise RuntimeError(f"Transcription request failed: {response.status_code} - {response.text}")
    
    transcript_id = response.json()['id']
    polling_endpoint = f"https://api.assemblyai.com/v2/transcript/{transcript_id}"
    
    # Poll for completion
    if verbose:
        print("Waiting for transcription to complete...")
    
    polling_duration = int(AudioFileClip(audio_file).duration * 2)  # 2x audio duration as timeout
    
    while polling_duration > 0:
        transcription_result = requests.get(polling_endpoint, headers=headers).json()
        
        if transcription_result['status'] == 'completed':
            if verbose:
                print("Transcription completed!")
            return transcription_result.get("words", [])
        elif transcription_result['status'] == 'error':
            raise RuntimeError(f"Transcription failed: {transcription_result['error']}")
        else:
            polling_duration -= 3
            time.sleep(3)
    
    raise RuntimeError("Transcription timed out")


def group_words_by_timeframe(words, time_frame_seconds):
    """
    Group words by time frames ensuring no overlaps or gaps between segments.
    
    Args:
        words (list): List of word dictionaries with "text", "start", "end" in milliseconds
        time_frame_seconds (int): Time frame in seconds to group words
        
    Returns:
        list: List of tuples (start_ms, end_ms, subtitle_text)
    """
    
    if not words:
        return []
    
    grouped_segments = []
    time_frame_ms = time_frame_seconds * 1000
    
    # Find the total duration
    if words:
        total_duration_ms = words[-1]["end"]
    else:
        return []
    
    # Create segments based on time frames
    current_time = 0
    
    while current_time < total_duration_ms:
        segment_end = current_time + time_frame_ms
        
        # Find words that fall within this time frame
        segment_words = []
        segment_start = None
        segment_end_actual = current_time
        
        for word in words:
            word_start = word["start"]
            word_end = word["end"]
            
            # Check if word overlaps with current time frame
            if word_start < segment_end and word_end > current_time:
                if segment_start is None:
                    segment_start = word_start
                segment_words.append(word["text"])
                segment_end_actual = max(segment_end_actual, word_end)
        
        if segment_words:
            # Use actual start time of first word in segment, but ensure it's at least current_time
            actual_start = max(segment_start, current_time)
            subtitle_text = " ".join(segment_words)
            grouped_segments.append((actual_start, segment_end_actual, subtitle_text))
            
            # Next segment starts exactly where this one ended
            current_time = segment_end_actual
        else:
            # If no words in this segment, move to next time frame
            current_time = segment_end
    
    return grouped_segments


def audio_to_csv(audio_file_path, time_frame_seconds, output_csv_path=None, api_key=None, verbose=True):
    """
    Convert audio file to CSV format with subtitles grouped by time frames.
    
    This function uses AssemblyAI for transcription to get word-level timestamps
    and groups them by specified time frames.
    
    Args:
        audio_file_path (str): Path to the audio file
        time_frame_seconds (int): Time frame in seconds to group subtitles (e.g., 4 for 4-second groups)
        output_csv_path (str, optional): Output CSV file path. If None, uses audio filename with .csv extension
        api_key (str, optional): AssemblyAI API key. If None, uses global API key
        verbose (bool): Whether to print progress messages
        
    Returns:
        str: Path to the generated CSV file
        
    Raises:
        FileNotFoundError: If audio file doesn't exist
        ValueError: If time_frame_seconds is invalid or API key not provided
        RuntimeError: For transcription errors
        
    Example:
        >>> csv_file = audio_to_csv("audio.mp3", 4, "output.csv")
        >>> print(f"Generated CSV: {csv_file}")
    """
    
    # Validate inputs
    if not os.path.exists(audio_file_path):
        raise FileNotFoundError(f"Audio file not found: {audio_file_path}")
    
    if time_frame_seconds <= 0:
        raise ValueError("Time frame must be a positive integer")
    
    # Use global API key if not provided
    if api_key is None:
        api_key = ASSEMBLYAI_API_KEY
    
    if not api_key:
        raise ValueError("AssemblyAI API key not found. Set it in Google Colab secrets or as environment variable.")
    
    # Generate output filename if not provided
    if output_csv_path is None:
        base_name = os.path.splitext(audio_file_path)[0]
        output_csv_path = f"{base_name}.csv"
    
    if verbose:
        print(f"Processing audio file: {audio_file_path}")
        print(f"Time frame: {time_frame_seconds} seconds")
        print(f"Output CSV: {output_csv_path}")
    
    try:
        # Step 1: Transcribe audio to get word-level timestamps
        words = transcribe_audio_with_words(audio_file_path, api_key, verbose)
        
        if not words:
            raise RuntimeError("No words found in transcription")
        
        if verbose:
            print(f"Transcribed {len(words)} words")
        
        # Step 2: Group words by time frame
        if verbose:
            print("Grouping words by time frame...")
        grouped_segments = group_words_by_timeframe(words, time_frame_seconds)
        
        # Step 3: Write to CSV with tab delimiter
        if verbose:
            print("Writing to CSV format...")
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            
            # Write header
            writer.writerow(['Start Time', 'Subtitle'])
            
            # Write subtitle data
            for start_ms, end_ms, subtitle_text in grouped_segments:
                # Format start and end times as HH:MM:SS,mmm --> HH:MM:SS,mmm
                start_time_str = ms_to_srt_time(start_ms)
                end_time_str = ms_to_srt_time(end_ms)
                time_range = f"{start_time_str} --> {end_time_str}"
                writer.writerow([time_range, subtitle_text.strip()])
        
        if verbose:
            print(f"Successfully created CSV file: {output_csv_path}")
            print(f"Total subtitle groups: {len(grouped_segments)}")
        
        return output_csv_path
        
    except Exception as e:
        if verbose:
            print(f"Error during conversion: {e}")
        raise


def get_csv_content(audio_file_path, time_frame_seconds, api_key=None, verbose=True):
    """
    Convert audio to CSV content without saving to file.
    
    Args:
        audio_file_path (str): Path to the audio file
        time_frame_seconds (int): Time frame in seconds to group subtitles
        api_key (str, optional): AssemblyAI API key. If None, uses global API key
        verbose (bool): Whether to print progress messages
        
    Returns:
        list: List of tuples containing (time_range, subtitle_text)
        
    Example:
        >>> content = get_csv_content("audio.mp3", 4)
        >>> for time_range, subtitle in content:
        ...     print(f"{time_range}: {subtitle}")
    """
    
    # Validate inputs
    if not os.path.exists(audio_file_path):
        raise FileNotFoundError(f"Audio file not found: {audio_file_path}")
    
    if time_frame_seconds <= 0:
        raise ValueError("Time frame must be a positive integer")
    
    # Use global API key if not provided
    if api_key is None:
        api_key = ASSEMBLYAI_API_KEY
    
    if not api_key:
        raise ValueError("AssemblyAI API key not found. Set it in Google Colab secrets or as environment variable.")
    
    if verbose:
        print(f"Processing audio file: {audio_file_path}")
        print(f"Time frame: {time_frame_seconds} seconds")
    
    try:
        # Step 1: Transcribe audio to get word-level timestamps
        words = transcribe_audio_with_words(audio_file_path, api_key, verbose)
        
        if not words:
            raise RuntimeError("No words found in transcription")
        
        if verbose:
            print(f"Transcribed {len(words)} words")
        
        # Step 2: Group words by time frame
        if verbose:
            print("Grouping words by time frame...")
        grouped_segments = group_words_by_timeframe(words, time_frame_seconds)
        
        # Step 3: Format content for return
        content = []
        for start_ms, end_ms, subtitle_text in grouped_segments:
            start_time_str = ms_to_srt_time(start_ms)
            end_time_str = ms_to_srt_time(end_ms)
            time_range = f"{start_time_str} --> {end_time_str}"
            content.append((time_range, subtitle_text.strip()))
        
        if verbose:
            print(f"Generated {len(content)} subtitle groups")
        
        return content
        
    except Exception as e:
        if verbose:
            print(f"Error during conversion: {e}")
        raise


def save_word_timestamps_to_json(audio_file_path, output_json_path=None, api_key=None, verbose=True):
    """
    Transcribe audio and save word-level timestamps to JSON file.
    
    Args:
        audio_file_path (str): Path to the audio file
        output_json_path (str, optional): Output JSON file path
        api_key (str, optional): AssemblyAI API key. If None, uses global API key
        verbose (bool): Whether to print progress messages
        
    Returns:
        str: Path to the generated JSON file
    """
    
    # Validate inputs
    if not os.path.exists(audio_file_path):
        raise FileNotFoundError(f"Audio file not found: {audio_file_path}")
    
    # Use global API key if not provided
    if api_key is None:
        api_key = ASSEMBLYAI_API_KEY
    
    if not api_key:
        raise ValueError("AssemblyAI API key not found. Set it in Google Colab secrets or as environment variable.")
    
    # Generate output filename if not provided
    if output_json_path is None:
        base_name = os.path.splitext(audio_file_path)[0]
        output_json_path = f"{base_name}_word_timestamps.json"
    
    if verbose:
        print(f"Processing audio file: {audio_file_path}")
        print(f"Output JSON: {output_json_path}")
    
    try:
        # Transcribe audio to get word-level timestamps
        words = transcribe_audio_with_words(audio_file_path, api_key, verbose)
        
        if not words:
            raise RuntimeError("No words found in transcription")
        
        # Save to JSON file
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(words, f, indent=2, ensure_ascii=False)
        
        if verbose:
            print(f"Successfully saved word timestamps to: {output_json_path}")
            print(f"Total words: {len(words)}")
        
        return output_json_path
        
    except Exception as e:
        if verbose:
            print(f"Error during transcription: {e}")
        raise


# Example usage
if __name__ == "__main__":
    # Example: Convert audio to CSV
    try:
        # Replace with your audio file path and desired time frame
        audio_file = "example_audio.mp3"
        time_frame = 4  # 4 seconds
        output_file = "output.csv"
        
        # API key is automatically loaded from Google Colab secrets or environment variable
        
        # Convert audio to CSV
        result = audio_to_csv(audio_file, time_frame, output_file)
        print(f"Successfully created: {result}")
        
        # Alternative: Get content without saving
        content = get_csv_content(audio_file, time_frame)
        print("\nFirst few entries:")
        for i, (time_range, subtitle) in enumerate(content[:3]):
            print(f"{i+1}. {time_range}: {subtitle}")
        
        # Alternative: Save word timestamps to JSON
        json_file = save_word_timestamps_to_json(audio_file)
        print(f"\nWord timestamps saved to: {json_file}")
            
    except Exception as e:
        print(f"Error: {e}")
        print("\nMake sure to set your AssemblyAI API key:")
        print("1. In Google Colab: Set secret 'ASSEMBLYAI_API_KEY'")
        print("2. Or set environment variable: export ASSEMBLYAI_API_KEY='your_api_key_here'") 