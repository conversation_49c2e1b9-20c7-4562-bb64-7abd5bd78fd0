import json
import re
import openai

from utils.aws_secrets import AWSSecrets

# --- Configuration ---
# Ensure you have your OpenAI API key set as an environment variable or replace the placeholder above.
openai.api_key = AWSSecrets().get_openai_cred()

# --- Constants ---
# These are the parameters for breaking the story
CHUNK_SIZE = 100  # Number of SRT blocks to process in one LLM call
OVERLAP_SIZE = 20  # Number of SRT blocks to include from the previous chunk for context


# --- LLM Interaction (Helper Function) ---
def call_llm(prompt, model="gpt-4-turbo-preview"):
    """Calls the OpenAI Chat API with a specific prompt and returns the JSON content."""
    print("--- Calling LLM ---")
    if openai.api_key == "YOUR_OPENAI_API_KEY":
        print("!!! WARNING: OpenAI API Key is a placeholder. The script will fail. !!!")
        print("!!! Please set the OPENAI_API_KEY environment variable. !!!")
        return None

    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant designed to output JSON."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"}
        )
        content = response.choices[0].message.content
        print("--- LLM Response Received ---")
        return content
    except Exception as e:
        print(f"An error occurred calling the LLM: {e}")
        return None


# --- Step 1: Parse SRT ---
### CHANGE ###
# This function now parses based on the SRT index number, not the raw file line number.
# This is a more robust and correct way to handle SRT files.
def parse_srt(srt_content: str) -> list:
    """Parses raw SRT content into a list of dialogue block objects, using the SRT index."""
    print("Parsing SRT content...")
    # Use a regex to be more robust against formatting errors like extra newlines
    srt_block_pattern = re.compile(r'(\d+)\n([\d:,]+ --> [\d:,]+)\n(.*?)\n\n', re.DOTALL)

    parsed_data = []
    for match in srt_block_pattern.finditer(srt_content):
        try:
            srt_index = int(match.group(1))
            timestamp = match.group(2).strip()
            # Join multi-line text blocks with a space
            text_content = " ".join(match.group(3).strip().split('\n'))

            parsed_data.append({
                "srt_index": srt_index,
                "timestamp": timestamp,
                "text": text_content
            })
        except (ValueError, IndexError) as e:
            print(f"Warning: Skipping malformed SRT block near match: {match.group(0)[:50]}... Error: {e}")

    # It's possible the file doesn't end with \n\n, so we try to grab the last block if it exists
    # This part is simplified; the regex usually handles most cases.

    print(f"Parsed {len(parsed_data)} SRT blocks.")
    return parsed_data


### CHANGE ###
# The entire scene detection logic is updated to work with `srt_index`.
def detect_scenes_in_chunks(parsed_srt: list) -> list:
    """Detects scenes by processing the SRT in overlapping chunks using SRT indices."""
    print("\n>>> STEP 2: DETECTING SCENES IN CHUNKS...")
    all_scenes = []
    last_processed_srt_index = 0
    scene_number = 1

    # The loop step is chunk_size - overlap_size to create the overlap
    for i in range(0, len(parsed_srt), CHUNK_SIZE - OVERLAP_SIZE):
        chunk_start_index_in_list = i
        chunk_end_index_in_list = min(i + CHUNK_SIZE, len(parsed_srt))
        chunk = parsed_srt[chunk_start_index_in_list:chunk_end_index_in_list]

        if not chunk:
            continue

        chunk_start_srt_index = chunk[0]['srt_index']
        print(f"\nProcessing chunk starting at SRT Index {chunk_start_srt_index}...")

        # Determine the cutoff index for new scene detection safely.
        analysis_start_index = 0
        if i == 0:
            # For the very first chunk, we analyze from its beginning.
            analysis_start_index = chunk[0]['srt_index']
        else:
            # For subsequent chunks, the new content starts at the block AFTER the overlap.
            if len(chunk) > OVERLAP_SIZE:
                analysis_start_index = chunk[OVERLAP_SIZE]['srt_index']
            else:
                # This chunk is fully contained within the overlap of the previous one. Nothing new to analyze.
                print(f"Skipping chunk at SRT index {chunk_start_srt_index} as it's fully overlapped.")
                continue

        # Create the text block for the LLM, mapping SRT indices for clarity
        chunk_text = ""
        for block in chunk:
            chunk_text += f"SRT {block['srt_index']}: {block['text']}\n"

        ### CHANGE ###
        # The prompt is updated to request and refer to SRT indices.
        prompt = f"""
        You are a script supervisor analyzing a chunk of a story from an SRT file. I will provide the text with its SRT index number.
        Your task is to identify scenes within this chunk. A scene is a continuous block of action defined by a consistent location, time, and character group.

        RULES:
        1. The chunk you are analyzing starts at SRT index {chunk_start_srt_index}.
        2. Because this is a chunk of a larger story, you must ONLY identify scenes that begin AT or AFTER SRT index {analysis_start_index}. Any text before this index is for context only.
        3. A new scene starts due to: Location Change, Time Jump, Character Grouping Shift, or Major Tone/Goal Shift.
        4. Your output must be a single JSON object with a key "scenes" containing an array.

        Each object in the "scenes" array must have:
        - "break_reason": A concise string explaining WHY this new scene started.
        - "start_srt_index": The integer SRT index number where the scene begins.
        - "end_srt_index": The integer SRT index number where the scene ends.

        Here is the chunk of text to analyze:
        ---
        {chunk_text}
        ---
        """

        response_json_str = call_llm(prompt)
        if not response_json_str:
            continue

        try:
            chunk_scenes = json.loads(response_json_str).get("scenes", [])

            # Stitching logic: Add scenes from the chunk, ensuring no overlap with previously processed scenes
            for scene in chunk_scenes:
                ### CHANGE ###
                # Logic now uses `start_srt_index` and updates `last_processed_srt_index`
                if scene.get('start_srt_index', 0) > last_processed_srt_index:
                    scene['scene_number'] = scene_number
                    all_scenes.append(scene)
                    last_processed_srt_index = scene.get('end_srt_index', last_processed_srt_index)
                    scene_number += 1

        except (json.JSONDecodeError, KeyError) as e:
            print(f"Error processing chunk response: {e}\nRaw Response: {response_json_str}")

    # Final pass to set the end_index of each scene to be the start_index of the next scene - 1
    # This ensures there are no gaps or overlaps in the final scene list.
    for i in range(len(all_scenes) - 1):
        all_scenes[i]['end_srt_index'] = all_scenes[i + 1]['start_srt_index'] - 1
    if all_scenes:
        # The last scene goes all the way to the end of the original file
        all_scenes[-1]['end_srt_index'] = parsed_srt[-1]['srt_index']

    print(f"\nSuccessfully detected a total of {len(all_scenes)} scenes from all chunks.")
    return all_scenes


# --- Step 3: Chapter Grouping (Works with SRT indices) ---
### CHANGE ###
# This function's prompt is updated to show SRT indices for better context.
def group_into_chapters(scenes: list) -> dict:
    """Groups scenes into chapters based on their summaries (break reasons)."""
    print("\n>>> STEP 3: GROUPING SCENES INTO CHAPTERS...")
    if not scenes:
        return {}

    # Create a summary of each scene including its SRT index range
    scene_summaries = [
        f"Scene {s['scene_number']} (SRT Indices {s['start_srt_index']}-{s['end_srt_index']}): {s['break_reason']}" for
        s in scenes]
    summaries_text = "\n".join(scene_summaries)

    prompt = f"""
    You are a master structural editor. Below are scene summaries from a story, including their SRT index ranges.
    Your task is to group these scenes into narratively coherent chapters. A chapter should cover a complete mini-arc or a single major event.

    Your output must be a single JSON object with a single key "chapters". The value should be an object where each key is a descriptive chapter title (e.g., "Chapter 1: The Confrontation") and the value is an array of the integer scene numbers belonging to that chapter.

    Ensure every scene number is assigned to exactly one chapter.

    SCENE SUMMARIES:
    ---
    {summaries_text}
    ---
    """
    response_json_str = call_llm(prompt)
    if not response_json_str:
        return {}

    try:
        chapter_map = json.loads(response_json_str).get("chapters", {})

        # Build the final structured output by replacing scene numbers with the full scene objects
        scene_lookup = {s['scene_number']: s for s in scenes}
        final_chapters = {}
        for chapter_title, scene_numbers in chapter_map.items():
            final_chapters[chapter_title] = [scene_lookup[num] for num in scene_numbers if num in scene_lookup]

        print(f"Successfully grouped scenes into {len(final_chapters)} chapters.")
        return final_chapters
    except json.JSONDecodeError:
        print(f"Error decoding chapter JSON: {response_json_str}")
        return {}


# --- Main Execution ---
def flow(srt_file_path):
    # To test this, you should use a real, longer SRT file.
    # Replace 'path/to/your/long_story.srt' with the actual file path.
    try:
        with open(srt_file_path, 'r',
                  encoding='utf-8') as f:
            srt_content = f.read()

        # 1. Parse the SRT to get a list of blocks with their SRT indices
        parsed_srt_data = parse_srt(srt_content)

        if not parsed_srt_data:
            print("Could not parse any data from the SRT file. Exiting.")
        else:
            # 2. Detect scenes using the chunking method based on SRT indices
            scenes = detect_scenes_in_chunks(parsed_srt_data)

            if scenes:
                print("\n--- DETECTED SCENES (using SRT indices) ---")
                print(json.dumps(scenes, indent=2))

                # 3. Group scenes into chapters
                final_structure = group_into_chapters(scenes)

                if final_structure:
                    print("\n--- FINAL CHAPTER STRUCTURE (using SRT indices) ---")
                    print(json.dumps(final_structure, indent=2))
                    return final_structure

    except FileNotFoundError:
        print("=" * 50)
        print("ERROR: SRT file not found.")
        print("Please replace 'path/to/your/long_story.srt' with a valid file path.")
        print("=" * 50)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


if __name__ == '__main__':
    srt = "/Users/<USER>/Downloads/466386_srt_file.srt"
    flow(srt)
